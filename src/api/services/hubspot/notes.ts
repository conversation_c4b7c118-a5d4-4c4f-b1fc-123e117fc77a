import { Client } from '@hubspot/api-client';
import { NoteRepository } from '../../repositories/note-repository';
import { HubSpotProgressTracker, ImportResult } from './progress-tracker';
import { HubSpotUtils } from './utils';
import { CompanyRepository } from '../../repositories/company-repository';
import { ContactRepository } from '../../repositories/contact-repository';
import { DealRepository } from '../../repositories/deal-repository';

/**
 * HubSpot notes and activities import functionality
 */
export class HubSpotNotes {
  private noteRepository: NoteRepository;
  private companyRepository: CompanyRepository;
  private contactRepository: ContactRepository;
  private dealRepository: DealRepository;
  private progressTracker: HubSpotProgressTracker;

  constructor(progressTracker: HubSpotProgressTracker) {
    this.noteRepository = new NoteRepository();
    this.companyRepository = new CompanyRepository();
    this.contactRepository = new ContactRepository();
    this.dealRepository = new DealRepository();
    this.progressTracker = progressTracker;
  }

  /**
   * Map HubSpot engagement type to our note type
   */
  private mapEngagementType(engagementType: string): string {
    const typeMapping: Record<string, string> = {
      'note': 'general',
      'email': 'email',
      'call': 'call',
      'meeting': 'meeting',
      'task': 'task',
      'incoming_email': 'email',
      'forwarded_email': 'email'
    };

    return typeMapping[engagementType.toLowerCase()] || 'general';
  }

  /**
   * Import notes and activities from HubSpot
   */
  async importNotesAndActivities(client: Client): Promise<ImportResult> {
    const errors: Array<{ item: string; error: string }> = [];
    const created: Array<{ item: string }> = [];
    let totalCount = 0;

    try {
      // Import notes for all entities
      console.log('Starting HubSpot notes and activities import...');
      
      // Get all companies, contacts, and deals that have HubSpot IDs
      const companies = this.companyRepository.getAllCompanies()
        .filter(c => c.hubspotId);
      const contacts = this.contactRepository.getAllContacts()
        .filter(c => c.hubspotId);
      const deals = this.dealRepository.getAllDeals()
        .filter(d => d.hubspotId);

      console.log(`[Notes] Found ${companies.length} companies, ${contacts.length} contacts, ${deals.length} deals with HubSpot IDs`);

      const totalEntities = companies.length + contacts.length + deals.length;
      this.progressTracker.startStep('notes', totalEntities);

      let processedCount = 0;

      // Import notes for companies
      for (const company of companies) {
        processedCount++;
        this.progressTracker.updateProgress(processedCount, `Processing notes for company: ${company.name}`);
        
        try {
          const result = await this.importNotesForEntity(client, 'company', company.hubspotId!, company.id);
          totalCount += result.count;
          created.push(...result.created);
          errors.push(...result.errors);
          
          if (result.count > 0) {
            console.log(`[Notes] Imported ${result.count} notes for company: ${company.name}`);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`[Notes] Error importing notes for company ${company.name}:`, error);
          errors.push({ item: `Company: ${company.name}`, error: errorMessage });
        }
      }

      // Import notes for contacts
      for (const contact of contacts) {
        processedCount++;
        const contactName = `${contact.firstName} ${contact.lastName}`;
        this.progressTracker.updateProgress(processedCount, `Processing notes for contact: ${contactName}`);
        
        try {
          const result = await this.importNotesForEntity(client, 'contact', contact.hubspotId!, contact.id);
          totalCount += result.count;
          created.push(...result.created);
          errors.push(...result.errors);
          
          if (result.count > 0) {
            console.log(`[Notes] Imported ${result.count} notes for contact: ${contactName}`);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`[Notes] Error importing notes for contact ${contactName}:`, error);
          errors.push({ item: `Contact: ${contactName}`, error: errorMessage });
        }
      }

      // Import notes for deals
      for (const deal of deals) {
        processedCount++;
        this.progressTracker.updateProgress(processedCount, `Processing notes for deal: ${deal.name}`);
        
        try {
          const result = await this.importNotesForEntity(client, 'deal', deal.hubspotId!, deal.id);
          totalCount += result.count;
          created.push(...result.created);
          errors.push(...result.errors);
          
          if (result.count > 0) {
            console.log(`[Notes] Imported ${result.count} notes for deal: ${deal.name}`);
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          console.error(`[Notes] Error importing notes for deal ${deal.name}:`, error);
          errors.push({ item: `Deal: ${deal.name}`, error: errorMessage });
        }
      }

      console.log(`Successfully imported ${totalCount} notes and activities`);

      return {
        success: errors.length === 0,
        count: totalCount,
        errors,
        updates: [],
        created
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error during notes import';
      console.error('Error during HubSpot notes import:', error);
      
      return {
        success: false,
        count: totalCount,
        errors: [...errors, { item: 'General', error: errorMessage }],
        updates: [],
        created
      };
    }
  }

  /**
   * Import notes for a specific entity
   */
  private async importNotesForEntity(
    client: Client,
    entityType: 'company' | 'contact' | 'deal',
    hubspotId: string,
    internalId: string
  ): Promise<ImportResult> {
    const errors: Array<{ item: string; error: string }> = [];
    const created: Array<{ item: string }> = [];
    let count = 0;

    try {
      // Get engagements for this entity
      let engagements: any[] = [];
      
      // HubSpot v3 API - get associated engagements
      const associations = await HubSpotUtils.withRetry(async () => {
        const pluralType = entityType === 'company' ? 'companies' : `${entityType}s`;
        
        // Get all engagement associations
        const response = await client.crm.associations.batchApi.read(pluralType, 'engagements', {
          inputs: [{ id: hubspotId }]
        });
        
        return response.results?.[0]?.to || [];
      });

      // Fetch details for each engagement
      for (const association of associations) {
        try {
          const engagement = await HubSpotUtils.withRetry(() =>
            client.crm.objects.basicApi.getById('engagements', association.id, [
              'hs_engagement_type',
              'hs_timestamp',
              'hs_engagement_subject',
              'hs_engagement_body',
              'hs_created_by',
              'hs_lastmodifieddate'
            ])
          );

          if (engagement) {
            engagements.push(engagement);
          }
        } catch (error) {
          console.warn(`Failed to fetch engagement ${association.id}:`, error);
        }
      }

      // Process each engagement
      for (const engagement of engagements) {
        try {
          const properties = engagement.properties;
          
          // Skip if no content
          const content = properties.hs_engagement_body || properties.hs_engagement_subject;
          if (!content) continue;

          // Check if note already exists
          const existingNotes = this.noteRepository.getNotesByEntity(entityType, internalId);
          const isDuplicate = existingNotes.some(note =>
            note.content === content &&
            note.createdAt === new Date(properties.hs_timestamp).toISOString()
          );

          if (!isDuplicate) {
            const noteData = {
              entityType,
              entityId: internalId,
              content: HubSpotUtils.sanitizePropertyValue(content) || '',
              noteType: this.mapEngagementType(properties.hs_engagement_type || 'note'),
              isPrivate: false,
              createdBy: 'HubSpot Import'
            };

            const createdNote = this.noteRepository.createNote(noteData);
            if (createdNote) {
              count++;
              created.push({ item: `${properties.hs_engagement_type || 'Note'}: ${content.substring(0, 50)}...` });
            }
          }
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          errors.push({ item: `Engagement ${engagement.id}`, error: errorMessage });
        }
      }

      return {
        success: errors.length === 0,
        count,
        errors,
        updates: [],
        created
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return {
        success: false,
        count: 0,
        errors: [{ item: `Entity ${hubspotId}`, error: errorMessage }],
        updates: [],
        created: []
      };
    }
  }
}