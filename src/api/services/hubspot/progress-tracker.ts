import { EventEmitter } from 'events';

// Progress tracking types
export interface ImportProgress {
  step: 'companies' | 'deals' | 'contacts';
  current: number;
  total: number;
  currentItem?: string;
  errors: Array<{ item: string; error: string }>;
}

export interface ImportResult {
  success: boolean;
  count: number;
  errors: Array<{ item: string; error: string }>;
  updates: Array<{ item: string; changes: string[] }>;
  created: Array<{ item: string }>;
  error?: string;
}

/**
 * Progress tracking for HubSpot imports with real-time updates
 */
export class HubSpotProgressTracker extends EventEmitter {
  private socketIO: any = null;
  private currentProgress: ImportProgress | null = null;

  /**
   * Set the Socket.IO instance for real-time progress updates
   */
  setSocketIO(io: any) {
    this.socketIO = io;
  }

  /**
   * Start tracking progress for a specific step
   */
  startStep(step: ImportProgress['step'], total: number): void {
    this.currentProgress = {
      step,
      current: 0,
      total,
      errors: []
    };

    this.emitProgress();
  }

  /**
   * Update progress for current step
   */
  updateProgress(current: number, currentItem?: string): void {
    if (!this.currentProgress) return;

    this.currentProgress.current = current;
    this.currentProgress.currentItem = currentItem;

    this.emitProgress();
  }

  /**
   * Add an error to the current step
   */
  addError(item: string, error: string): void {
    if (!this.currentProgress) return;

    this.currentProgress.errors.push({ item, error });
    this.emitProgress();
  }

  /**
   * Complete the current step
   */
  completeStep(): void {
    if (!this.currentProgress) return;

    this.currentProgress.current = this.currentProgress.total;
    this.emitProgress();
    this.currentProgress = null;
  }

  /**
   * Emit progress update via Socket.IO and events
   */
  private emitProgress(): void {
    if (!this.currentProgress) return;

    // Emit via EventEmitter for local listeners
    this.emit('progress', this.currentProgress);

    // Emit via Socket.IO for real-time updates to frontend
    if (this.socketIO) {
      this.socketIO.emit('hubspot_progress', this.currentProgress);
    }
  }

  /**
   * Get current progress
   */
  getCurrentProgress(): ImportProgress | null {
    return this.currentProgress;
  }

  /**
   * Reset progress tracker
   */
  reset(): void {
    this.currentProgress = null;
  }

  /**
   * Create an import result object
   */
  createResult(data: {
    success: boolean;
    count: number;
    errors?: Array<{ item: string; error: string }>;
    updates?: Array<{ item: string; changes: string[] }>;
    created?: Array<{ item: string }>;
    error?: string;
  }): ImportResult {
    return {
      success: data.success,
      count: data.count,
      errors: data.errors || [],
      updates: data.updates || [],
      created: data.created || [],
      error: data.error
    };
  }

  /**
   * Track processing of a batch of items
   */
  async trackBatch<T, R>(
    items: T[],
    processor: (item: T, index: number) => Promise<R>,
    itemName: (item: T) => string
  ): Promise<R[]> {
    const results: R[] = [];

    for (let i = 0; i < items.length; i++) {
      const item = items[i];
      const name = itemName(item);

      try {
        this.updateProgress(i + 1, name);
        const result = await processor(item, i);
        results.push(result);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        this.addError(name, errorMessage);
        console.error(`Error processing ${name}:`, error);
      }
    }

    return results;
  }
}