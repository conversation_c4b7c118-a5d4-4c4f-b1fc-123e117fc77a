/**
 * @file Centralized color constants and utilities
 * 
 * This file provides a comprehensive color system for the application,
 * including hex colors for charts, Tailwind classes for UI components,
 * and specialized color schemes for different features.
 */

/**
 * Core color palette using Tailwind color values
 * These colors are used consistently across different features
 */
export const coreColors = {
  // Primary blues
  blue: {
    100: '#dbeafe',
    200: '#bfdbfe', 
    300: '#93c5fd',
    500: '#3b82f6',
    600: '#2563eb',
    800: '#1e40af'
  },
  // Secondary colors
  emerald: {
    100: '#d1fae5',
    200: '#a7f3d0',
    300: '#6ee7b7',
    500: '#10b981',
    600: '#059669'
  },
  purple: {
    100: '#e9d5ff',
    200: '#ddd6fe',
    300: '#c4b5fd',
    500: '#8b5cf6',
    600: '#7c3aed'
  },
  // Additional colors
  teal: {
    200: '#99f6e4',
    300: '#5eead4',
    500: '#14b8a6',
    600: '#0d9488'
  },
  amber: {
    100: '#fef3c7',
    200: '#fde68a',
    500: '#f59e0b',
    600: '#d97706',
    800: '#92400e'
  },
  // Status colors
  red: {
    100: '#fee2e2',
    200: '#fecaca',
    500: '#ef4444',
    800: '#991b1b'
  },
  green: {
    100: '#dcfce7',
    200: '#bbf7d0', 
    500: '#22c55e',
    800: '#166534'
  },
  // Neutral colors
  gray: {
    100: '#f3f4f6',
    200: '#e5e7eb',
    600: '#4b5563',
    800: '#1f2937'
  },
  slate: {
    100: '#f1f5f9',
    200: '#e2e8f0',
    700: '#334155',
    800: '#1e293b'
  }
};

/**
 * Task visualization colors (hex format for charts)
 * Used in chart libraries like Recharts
 */
export const taskColors = [
  '#3b82f6', // blue-500 - Billable tasks
  '#f97316', // orange-500 - Public Holiday
  '#8b5cf6', // violet-500 - Annual Leave
  '#14b8a6', // teal-500 - Other Unpaid Leave
  '#f59e0b', // amber-500 - Salesforce
  '#ec4899', // pink-500 - Interviews
  '#10b981', // emerald-500
  '#6366f1', // indigo-500
  '#84cc16', // lime-500
  '#06b6d4', // cyan-500
  '#d946ef', // fuchsia-500
  '#0ea5e9', // sky-500
  '#22c55e', // green-500
  '#ef4444', // red-500
  '#a855f7', // purple-500
  '#64748b', // slate-500
  '#ca8a04', // yellow-600
  '#0891b2', // cyan-600
  '#4f46e5', // indigo-600
  '#be123c', // rose-700
];

/**
 * Expense type color mapping
 * Used for expense categorization badges
 */
export const expenseTypeColors = {
  'Monthly Payroll': 'bg-blue-100 text-blue-800 border-blue-200 dark:border-blue-800/30',
  'Superannuation': 'bg-amber-100 text-amber-800 border-amber-200 dark:border-amber-800/30',
  'Insurances': 'bg-pink-100 text-pink-800 border-pink-200 dark:border-pink-800/30',
  'Taxes': 'bg-indigo-100 text-indigo-800 border-indigo-200 dark:border-indigo-800/30',
  'Subcontractor Fees': 'bg-purple-100 text-purple-800 border-purple-200 dark:border-purple-800/30',
  'Rent': 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:border-emerald-800/30',
  'Reimbursements': 'bg-teal-100 text-teal-800 border-teal-200 dark:border-teal-800/30',
  'Professional Fees': 'bg-cyan-100 text-cyan-800 border-cyan-200 dark:border-cyan-800/30',
  'General Expenses': 'bg-sky-100 text-sky-800 border-sky-200 dark:border-sky-800/30',
  'Director Distributions': 'bg-violet-100 text-violet-800 border-violet-200 dark:border-violet-800/30',
  'Hardware': 'bg-fuchsia-100 text-fuchsia-800 border-fuchsia-200 dark:border-fuchsia-800/30',
  'Subscriptions': 'bg-rose-100 text-rose-800 border-rose-200 dark:border-rose-800/30',
  'Other Fees': 'bg-orange-100 text-orange-800 border-orange-200 dark:border-orange-800/30',
  'Other': 'bg-gray-100 text-gray-800 border-gray-200 dark:border-gray-600'
} as const;

/**
 * Expense frequency color mapping
 * Used for frequency badges
 */
export const expenseFrequencyColors = {
  'one-off': 'bg-slate-100 text-slate-800 border-slate-200 dark:border-slate-700',
  'weekly': 'bg-blue-100 text-blue-800 border-blue-200 dark:border-blue-800/30',
  'monthly': 'bg-violet-100 text-violet-800 border-violet-200 dark:border-violet-800/30',
  'quarterly': 'bg-orange-100 text-orange-800 border-orange-200 dark:border-orange-800/30'
} as const;

/**
 * Tax calendar color schemes
 * Used for PAYGW and GST calendar displays
 */
export const taxCalendarColors = {
  paygw: {
    light: [
      'bg-blue-200', 'bg-emerald-200', 'bg-purple-200', 'bg-teal-200',
      'bg-violet-200', 'bg-green-200', 'bg-indigo-200', 'bg-cyan-200',
      'bg-blue-300', 'bg-emerald-300', 'bg-purple-300', 'bg-teal-300'
    ],
    dark: [
      'bg-blue-500', 'bg-emerald-500', 'bg-purple-500', 'bg-teal-500',
      'bg-violet-500', 'bg-green-500', 'bg-indigo-500', 'bg-cyan-500',
      'bg-blue-600', 'bg-emerald-600', 'bg-purple-600', 'bg-teal-600'
    ],
    payment: {
      light: [
        'bg-blue-200 border-2 border-blue-500',
        'bg-emerald-200 border-2 border-emerald-500',
        'bg-purple-200 border-2 border-purple-500',
        'bg-teal-200 border-2 border-teal-500',
        'bg-violet-200 border-2 border-violet-500',
        'bg-green-200 border-2 border-green-500',
        'bg-indigo-200 border-2 border-indigo-500',
        'bg-cyan-200 border-2 border-cyan-500',
        'bg-blue-300 border-2 border-blue-600',
        'bg-emerald-300 border-2 border-emerald-600',
        'bg-purple-300 border-2 border-purple-600',
        'bg-teal-300 border-2 border-teal-600'
      ],
      dark: [
        'bg-blue-500 border-2 border-blue-300',
        'bg-emerald-500 border-2 border-emerald-300',
        'bg-purple-500 border-2 border-purple-300',
        'bg-teal-500 border-2 border-teal-300',
        'bg-violet-500 border-2 border-violet-300',
        'bg-green-500 border-2 border-green-300',
        'bg-indigo-500 border-2 border-indigo-300',
        'bg-cyan-500 border-2 border-cyan-300',
        'bg-blue-600 border-2 border-blue-200',
        'bg-emerald-600 border-2 border-emerald-200',
        'bg-purple-600 border-2 border-purple-200',
        'bg-teal-600 border-2 border-teal-200'
      ]
    }
  },
  gst: {
    light: ['bg-yellow-200', 'bg-red-200', 'bg-orange-300', 'bg-pink-300'],
    dark: ['bg-yellow-500', 'bg-red-500', 'bg-orange-600', 'bg-pink-600'],
    payment: {
      light: [
        'bg-yellow-200 border-2 border-yellow-600',
        'bg-red-200 border-2 border-red-600',
        'bg-orange-300 border-2 border-orange-600',
        'bg-pink-300 border-2 border-pink-600'
      ],
      dark: [
        'bg-yellow-500 border-2 border-yellow-300',
        'bg-red-500 border-2 border-red-300',
        'bg-orange-600 border-2 border-orange-300',
        'bg-pink-600 border-2 border-pink-300'
      ]
    }
  }
};

/**
 * Financial color standards
 * Consistent colors for financial data representation
 */
export const financialColors = {
  income: {
    light: 'text-green-600',
    dark: 'text-green-400',
    bg: 'bg-green-100 dark:bg-green-900/30'
  },
  expense: {
    light: 'text-red-600', 
    dark: 'text-red-400',
    bg: 'bg-red-100 dark:bg-red-900/30'
  },
  neutral: {
    light: 'text-gray-600',
    dark: 'text-gray-400',
    bg: 'bg-gray-100 dark:bg-gray-900/30'
  }
};

/**
 * Status color scheme
 * For various status indicators throughout the app
 */
export const statusColors = {
  success: 'bg-green-100 text-green-800 border-green-200 dark:bg-green-900/30 dark:text-green-300',
  warning: 'bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900/30 dark:text-yellow-300',
  error: 'bg-red-100 text-red-800 border-red-200 dark:bg-red-900/30 dark:text-red-300',
  info: 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900/30 dark:text-blue-300',
  neutral: 'bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-700 dark:text-gray-300'
};

/**
 * Utility functions for color management
 */

/**
 * Get a color for a task based on its ID and properties
 * Ensures consistent colors across different views
 */
export const getTaskColor = (taskId: number, taskName: string, isBillable: boolean): string => {
  // Billable tasks are always blue
  if (isBillable) {
    return taskColors[0];
  }

  // Map common task names to specific colors
  const commonTaskColors: Record<string, string> = {
    'Public Holiday': taskColors[1],
    'Annual Leave': taskColors[2], 
    'Other Unpaid Leave': taskColors[3],
    'Salesforce': taskColors[4],
    'Interviews': taskColors[5],
  };

  // Check if this is a common task with a predefined color
  if (commonTaskColors[taskName]) {
    return commonTaskColors[taskName];
  }

  // For other non-billable tasks, use a hash of the task ID to pick a consistent color
  const hash = taskId % (taskColors.length - 1);
  return taskColors[hash + 1]; // +1 to skip the billable color
};

/**
 * Get CSS class for expense type
 */
export const getExpenseTypeClass = (type: keyof typeof expenseTypeColors | string): string => {
  return expenseTypeColors[type as keyof typeof expenseTypeColors] || expenseTypeColors['Other'];
};

/**
 * Get CSS class for expense frequency
 */
export const getExpenseFrequencyClass = (frequency: keyof typeof expenseFrequencyColors | string): string => {
  return expenseFrequencyColors[frequency as keyof typeof expenseFrequencyColors] || statusColors.neutral;
};

/**
 * Get tax calendar color class
 */
export const getTaxCalendarColor = (
  type: 'paygw' | 'gst',
  index: number,
  isPayment: boolean,
  isDarkMode: boolean,
  isHighlighted: boolean
): string => {
  const colorSet = taxCalendarColors[type];
  
  // Determine which color variant to use based on theme and highlight state
  const variant = isDarkMode 
    ? (isHighlighted ? 'light' : 'dark')
    : (isHighlighted ? 'dark' : 'light');
  
  if (isPayment) {
    return colorSet.payment[variant][index % colorSet.payment[variant].length];
  }
  
  return colorSet[variant][index % colorSet[variant].length];
};

/**
 * Get financial color based on value (positive/negative)
 */
export const getFinancialColor = (value: number, isDarkMode = false): string => {
  if (value > 0) {
    return isDarkMode ? financialColors.income.dark : financialColors.income.light;
  } else if (value < 0) {
    return isDarkMode ? financialColors.expense.dark : financialColors.expense.light;
  }
  return isDarkMode ? financialColors.neutral.dark : financialColors.neutral.light;
};

/**
 * Get a set of colors for a collection of tasks
 * This ensures each task gets a unique color when possible
 */
export const getTaskColorMap = (
  tasks: Array<{ taskId: number; taskName: string; isBillable: boolean }>
): Record<number, string> => {
  const colorMap: Record<number, string> = {};
  const usedColors = new Set<string>();

  // Common task name to color mapping for consistency
  const commonTaskColors: Record<string, string> = {
    'Billable': taskColors[0],
    'Public Holiday': taskColors[1],
    'Annual Leave': taskColors[2],
    'Other Unpaid Leave': taskColors[3],
    'Salesforce': taskColors[4],
    'Interviews': taskColors[5],
  };

  // First, assign colors to billable tasks (all blue)
  tasks.filter(t => t.isBillable).forEach(task => {
    colorMap[task.taskId] = taskColors[0];
  });

  // Then, assign unique colors to non-billable tasks
  const nonBillableTasks = tasks.filter(t => !t.isBillable);

  // Sort by task ID to ensure consistent color assignment
  nonBillableTasks.sort((a, b) => a.taskId - b.taskId);

  nonBillableTasks.forEach(task => {
    // First check if this is a common task with a predefined color
    if (commonTaskColors[task.taskName]) {
      colorMap[task.taskId] = commonTaskColors[task.taskName];
      usedColors.add(commonTaskColors[task.taskName]);
      return;
    }

    // Try to get a color based on task ID
    let colorIndex = (task.taskId % (taskColors.length - 1)) + 1; // +1 to skip billable color
    let color = taskColors[colorIndex];

    // If this color is already used, find another one
    if (usedColors.has(color)) {
      // Find the first unused color
      for (let i = 1; i < taskColors.length; i++) {
        if (!usedColors.has(taskColors[i])) {
          color = taskColors[i];
          break;
        }
      }

      // If all colors are used, fall back to the original algorithm
      if (usedColors.has(color)) {
        color = taskColors[colorIndex];
      }
    }

    colorMap[task.taskId] = color;
    usedColors.add(color);
  });

  return colorMap;
};